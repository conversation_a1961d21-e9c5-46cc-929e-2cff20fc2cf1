using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using System;
using System.Windows;
using System.Windows.Shapes;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Linq;

namespace DriverManagementSystem.Views
{
    public partial class DashboardView : UserControl
    {
        public DashboardView()
        {
            InitializeComponent();
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 إنشاء DashboardView");
                var viewModel = new DashboardViewModel();
                DataContext = viewModel;
                System.Diagnostics.Debug.WriteLine($"✅ تم ربط ViewModel - عدد الأزرار: {viewModel.QuickActions.Count}");

                // تهيئة المخططات بعد تحميل الواجهة
                Loaded += DashboardView_Loaded;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء DashboardView: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void DashboardView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تأخير بسيط للتأكد من تحميل الواجهة كاملة
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    DrawVisitsChart();
                    DrawProjectsChart();
                    DrawFinancialChart();
                    DrawPerformanceChart();
                    DrawDriversStatusChart();
                }), System.Windows.Threading.DispatcherPriority.Loaded);

                System.Diagnostics.Debug.WriteLine("✅ تم جدولة رسم جميع المخططات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في رسم المخططات: {ex.Message}");
            }
        }

        /// <summary>
        /// رسم مخطط الزيارات الشهرية الاحترافي
        /// </summary>
        private void DrawVisitsChart()
        {
            var canvas = FindName("VisitsChart") as Canvas;
            if (canvas == null) return;

            canvas.Children.Clear();

            // بيانات الزيارات (25, 35, 18, 42, 28, 38)
            var data = new double[] { 25, 35, 18, 42, 28, 38 };
            var maxValue = 50.0;
            var chartWidth = 280.0;
            var chartHeight = 200.0;
            var barWidth = chartWidth / data.Length * 0.6;
            var spacing = chartWidth / data.Length;

            for (int i = 0; i < data.Length; i++)
            {
                var barHeight = (data[i] / maxValue) * chartHeight;
                var x = 40 + i * spacing;
                var y = chartHeight - barHeight + 50;

                // رسم العمود مع تدرج لوني
                var rect = new Rectangle
                {
                    Width = barWidth,
                    Height = barHeight,
                    RadiusX = 8,
                    RadiusY = 8
                };

                // تدرج لوني احترافي
                var gradient = new LinearGradientBrush();
                gradient.StartPoint = new Point(0, 0);
                gradient.EndPoint = new Point(0, 1);
                gradient.GradientStops.Add(new GradientStop(Color.FromRgb(33, 150, 243), 0)); // #2196F3
                gradient.GradientStops.Add(new GradientStop(Color.FromRgb(25, 118, 210), 1)); // #1976D2
                rect.Fill = gradient;

                // إضافة ظل
                rect.Effect = new DropShadowEffect
                {
                    Color = Color.FromRgb(33, 150, 243),
                    Direction = 270,
                    ShadowDepth = 4,
                    Opacity = 0.3,
                    BlurRadius = 8
                };

                Canvas.SetLeft(rect, x);
                Canvas.SetTop(rect, y);
                canvas.Children.Add(rect);

                // رسم القيمة فوق العمود
                var textBlock = new TextBlock
                {
                    Text = data[i].ToString(),
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    FontFamily = new FontFamily("Segoe UI"),
                    FlowDirection = FlowDirection.LeftToRight
                };

                Canvas.SetLeft(textBlock, x + barWidth / 2 - 8);
                Canvas.SetTop(textBlock, y - 25);
                canvas.Children.Add(textBlock);

                // إضافة تأثير الانعكاس
                var reflection = new Rectangle
                {
                    Width = barWidth,
                    Height = barHeight * 0.3,
                    RadiusX = 8,
                    RadiusY = 8,
                    Fill = new LinearGradientBrush
                    {
                        StartPoint = new Point(0, 0),
                        EndPoint = new Point(0, 1),
                        GradientStops = new GradientStopCollection
                        {
                            new GradientStop(Color.FromArgb(50, 33, 150, 243), 0),
                            new GradientStop(Color.FromArgb(10, 33, 150, 243), 1)
                        }
                    }
                };

                Canvas.SetLeft(reflection, x);
                Canvas.SetTop(reflection, y + barHeight + 5);
                canvas.Children.Add(reflection);
            }
        }

        /// <summary>
        /// رسم مخطط المشاريع الاحترافي
        /// </summary>
        private void DrawProjectsChart()
        {
            var canvas = FindName("ProjectsChart") as Canvas;
            if (canvas == null) return;

            canvas.Children.Clear();

            // بيانات المشاريع (12, 18, 15, 22)
            var data = new double[] { 12, 18, 15, 22 };
            var maxValue = 25.0;
            var chartWidth = 280.0;
            var chartHeight = 200.0;
            var barWidth = chartWidth / data.Length * 0.6;
            var spacing = chartWidth / data.Length;

            for (int i = 0; i < data.Length; i++)
            {
                var barHeight = (data[i] / maxValue) * chartHeight;
                var x = 40 + i * spacing;
                var y = chartHeight - barHeight + 50;

                // رسم العمود مع تدرج برتقالي
                var rect = new Rectangle
                {
                    Width = barWidth,
                    Height = barHeight,
                    RadiusX = 8,
                    RadiusY = 8
                };

                // تدرج لوني برتقالي احترافي
                var gradient = new LinearGradientBrush();
                gradient.StartPoint = new Point(0, 0);
                gradient.EndPoint = new Point(0, 1);
                gradient.GradientStops.Add(new GradientStop(Color.FromRgb(255, 152, 0), 0)); // #FF9800
                gradient.GradientStops.Add(new GradientStop(Color.FromRgb(245, 124, 0), 1)); // #F57C00
                rect.Fill = gradient;

                // إضافة ظل برتقالي
                rect.Effect = new DropShadowEffect
                {
                    Color = Color.FromRgb(255, 152, 0),
                    Direction = 270,
                    ShadowDepth = 4,
                    Opacity = 0.4,
                    BlurRadius = 10
                };

                Canvas.SetLeft(rect, x);
                Canvas.SetTop(rect, y);
                canvas.Children.Add(rect);

                // رسم القيمة داخل العمود
                var textBlock = new TextBlock
                {
                    Text = data[i].ToString(),
                    FontSize = 14,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    FontFamily = new FontFamily("Segoe UI"),
                    FlowDirection = FlowDirection.LeftToRight
                };

                Canvas.SetLeft(textBlock, x + barWidth / 2 - 8);
                Canvas.SetTop(textBlock, y + barHeight / 2 - 7);
                canvas.Children.Add(textBlock);

                // إضافة خط إضاءة في الأعلى
                var highlight = new Rectangle
                {
                    Width = barWidth,
                    Height = 4,
                    RadiusX = 8,
                    RadiusY = 8,
                    Fill = new SolidColorBrush(Color.FromArgb(100, 255, 255, 255))
                };

                Canvas.SetLeft(highlight, x);
                Canvas.SetTop(highlight, y);
                canvas.Children.Add(highlight);
            }
        }

        /// <summary>
        /// رسم مخطط حالة السائقين الدائري الاحترافي
        /// </summary>
        private void DrawDriversStatusChart()
        {
            var canvas = FindName("DriversStatusChart") as Canvas;
            if (canvas == null) return;

            canvas.Children.Clear();

            // بيانات السائقين
            var data = new[] { 45.0, 25.0, 20.0, 10.0 }; // في الميدان، في المكتب، في إجازة، غير متاح
            var colors = new[] { "#2196F3", "#4CAF50", "#FF9800", "#F44336" };
            var total = data.Sum();

            // مركز الدائرة
            var centerX = 150.0;
            var centerY = 150.0;
            var radius = 80.0;
            var innerRadius = 50.0;

            double currentAngle = -90; // البدء من الأعلى

            for (int i = 0; i < data.Length; i++)
            {
                var percentage = data[i] / total;
                var sweepAngle = percentage * 360;

                // رسم قطاع الدائرة
                var pathGeometry = new PathGeometry();
                var pathFigure = new PathFigure();

                // النقطة الخارجية الأولى
                var startAngleRad = currentAngle * Math.PI / 180;
                var endAngleRad = (currentAngle + sweepAngle) * Math.PI / 180;

                var startPointOuter = new Point(
                    centerX + radius * Math.Cos(startAngleRad),
                    centerY + radius * Math.Sin(startAngleRad));

                var endPointOuter = new Point(
                    centerX + radius * Math.Cos(endAngleRad),
                    centerY + radius * Math.Sin(endAngleRad));

                var startPointInner = new Point(
                    centerX + innerRadius * Math.Cos(startAngleRad),
                    centerY + innerRadius * Math.Sin(startAngleRad));

                var endPointInner = new Point(
                    centerX + innerRadius * Math.Cos(endAngleRad),
                    centerY + innerRadius * Math.Sin(endAngleRad));

                pathFigure.StartPoint = startPointOuter;

                // القوس الخارجي
                var arcSegmentOuter = new ArcSegment
                {
                    Point = endPointOuter,
                    Size = new Size(radius, radius),
                    IsLargeArc = sweepAngle > 180,
                    SweepDirection = SweepDirection.Clockwise
                };

                // خط إلى الدائرة الداخلية
                var lineToInner = new LineSegment { Point = endPointInner };

                // القوس الداخلي (عكس الاتجاه)
                var arcSegmentInner = new ArcSegment
                {
                    Point = startPointInner,
                    Size = new Size(innerRadius, innerRadius),
                    IsLargeArc = sweepAngle > 180,
                    SweepDirection = SweepDirection.Counterclockwise
                };

                pathFigure.Segments.Add(arcSegmentOuter);
                pathFigure.Segments.Add(lineToInner);
                pathFigure.Segments.Add(arcSegmentInner);
                pathFigure.IsClosed = true;

                pathGeometry.Figures.Add(pathFigure);

                var path = new Path
                {
                    Data = pathGeometry,
                    Fill = GetBrushFromHex(colors[i]),
                    Stroke = Brushes.White,
                    StrokeThickness = 2
                };

                // إضافة تأثير الظل
                path.Effect = new DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 3,
                    Opacity = 0.3,
                    BlurRadius = 8
                };

                canvas.Children.Add(path);

                currentAngle += sweepAngle;
            }
        }

        /// <summary>
        /// رسم مخطط التحليل المالي
        /// </summary>
        private void DrawFinancialChart()
        {
            var canvas = FindName("FinancialChart") as Canvas;
            if (canvas == null) return;

            canvas.Children.Clear();

            // بيانات الميزانية والمصروفات
            var budgetData = new double[] { 150, 180, 165, 220, 195, 250 };
            var expenseData = new double[] { 120, 145, 135, 185, 170, 210 };
            var maxValue = 300.0;
            var chartWidth = 280.0;
            var chartHeight = 200.0;
            var barWidth = chartWidth / budgetData.Length * 0.3;
            var spacing = chartWidth / budgetData.Length;

            for (int i = 0; i < budgetData.Length; i++)
            {
                var x = 40 + i * spacing;

                // رسم عمود الميزانية
                var budgetHeight = (budgetData[i] / maxValue) * chartHeight;
                var budgetY = chartHeight - budgetHeight + 50;

                var budgetRect = new Rectangle
                {
                    Width = barWidth,
                    Height = budgetHeight,
                    Fill = new SolidColorBrush(Color.FromRgb(156, 39, 176)), // #9C27B0
                    RadiusX = 3,
                    RadiusY = 3
                };

                Canvas.SetLeft(budgetRect, x);
                Canvas.SetTop(budgetRect, budgetY);
                canvas.Children.Add(budgetRect);

                // رسم عمود المصروفات
                var expenseHeight = (expenseData[i] / maxValue) * chartHeight;
                var expenseY = chartHeight - expenseHeight + 50;

                var expenseRect = new Rectangle
                {
                    Width = barWidth,
                    Height = expenseHeight,
                    Fill = new SolidColorBrush(Color.FromRgb(233, 30, 99)), // #E91E63
                    RadiusX = 3,
                    RadiusY = 3
                };

                Canvas.SetLeft(expenseRect, x + barWidth + 5);
                Canvas.SetTop(expenseRect, expenseY);
                canvas.Children.Add(expenseRect);
            }
        }

        /// <summary>
        /// رسم مخطط الأداء الشامل
        /// </summary>
        private void DrawPerformanceChart()
        {
            var canvas = FindName("PerformanceChart") as Canvas;
            if (canvas == null) return;

            canvas.Children.Clear();

            // بيانات الأداء
            var visitsData = new Point[] {
                new Point(50, 300), new Point(100, 270), new Point(150, 230), new Point(200, 200),
                new Point(250, 170), new Point(300, 130), new Point(350, 100), new Point(400, 70)
            };

            var contractsData = new Point[] {
                new Point(50, 280), new Point(100, 250), new Point(150, 220), new Point(200, 190),
                new Point(250, 150), new Point(300, 120), new Point(350, 90), new Point(400, 60)
            };

            var budgetData = new Point[] {
                new Point(50, 290), new Point(100, 260), new Point(150, 240), new Point(200, 210),
                new Point(250, 180), new Point(300, 140), new Point(350, 110), new Point(400, 80)
            };

            // رسم خطوط الأداء
            DrawLine(canvas, visitsData, Color.FromRgb(33, 150, 243)); // #2196F3
            DrawLine(canvas, contractsData, Color.FromRgb(76, 175, 80)); // #4CAF50
            DrawLine(canvas, budgetData, Color.FromRgb(255, 152, 0)); // #FF9800

            // رسم النقاط
            DrawPoints(canvas, visitsData, Color.FromRgb(33, 150, 243));
            DrawPoints(canvas, contractsData, Color.FromRgb(76, 175, 80));
            DrawPoints(canvas, budgetData, Color.FromRgb(255, 152, 0));
        }

        /// <summary>
        /// رسم خط بين النقاط
        /// </summary>
        private void DrawLine(Canvas canvas, Point[] points, Color color)
        {
            for (int i = 0; i < points.Length - 1; i++)
            {
                var line = new Line
                {
                    X1 = points[i].X,
                    Y1 = points[i].Y,
                    X2 = points[i + 1].X,
                    Y2 = points[i + 1].Y,
                    Stroke = new SolidColorBrush(color),
                    StrokeThickness = 3
                };

                canvas.Children.Add(line);
            }
        }

        /// <summary>
        /// رسم النقاط على الخط
        /// </summary>
        private void DrawPoints(Canvas canvas, Point[] points, Color color)
        {
            foreach (var point in points)
            {
                var ellipse = new Ellipse
                {
                    Width = 8,
                    Height = 8,
                    Fill = new SolidColorBrush(color)
                };

                Canvas.SetLeft(ellipse, point.X - 4);
                Canvas.SetTop(ellipse, point.Y - 4);
                canvas.Children.Add(ellipse);
            }
        }

        /// <summary>
        /// تحويل اللون من Hex إلى SolidColorBrush
        /// </summary>
        private SolidColorBrush GetBrushFromHex(string hexColor)
        {
            try
            {
                // إزالة # إذا كانت موجودة
                hexColor = hexColor.Replace("#", "");

                // تحويل إلى RGB
                byte r = Convert.ToByte(hexColor.Substring(0, 2), 16);
                byte g = Convert.ToByte(hexColor.Substring(2, 2), 16);
                byte b = Convert.ToByte(hexColor.Substring(4, 2), 16);

                return new SolidColorBrush(Color.FromRgb(r, g, b));
            }
            catch
            {
                // في حالة الخطأ، إرجاع لون افتراضي
                return new SolidColorBrush(Color.FromRgb(33, 150, 243));
            }
        }
    }
}
