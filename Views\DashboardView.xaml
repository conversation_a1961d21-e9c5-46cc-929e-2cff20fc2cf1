<UserControl x:Class="DriverManagementSystem.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"

             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- تعريف الألوان للمخططات -->
            <SolidColorBrush x:Key="ChartPrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="ChartSecondaryBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="ChartAccentBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ChartDangerBrush" Color="#F44336"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- التبويبات الرئيسية -->
        <TabControl Background="Transparent"
                    BorderThickness="0"
                    Margin="20">
            <TabControl.Resources>
                <!-- تصميم التبويبات -->
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Border"
                                        Background="White"
                                        BorderBrush="#E0E0E0"
                                        BorderThickness="1,1,1,0"
                                        CornerRadius="10,10,0,0"
                                        Margin="2,0"
                                        Padding="20,12">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                                    </Border.Effect>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Tag}"
                                                   FontSize="18"
                                                   Margin="0,0,8,0"
                                                   VerticalAlignment="Center"/>
                                        <ContentPresenter ContentSource="Header"
                                                          VerticalAlignment="Center"
                                                          HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#2196F3"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Setter Property="FontSize" Value="16"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                    <Setter Property="Foreground" Value="#2C3E50"/>
                </Style>
            </TabControl.Resources>

            <!-- تبويب الوصول السريع -->
            <TabItem Header="الوصول السريع" Tag="🚀">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="20">
                    <StackPanel>

                        <!-- Page Header -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Orientation="Horizontal" Grid.Column="0">
                                <TextBlock Text="🏠"
                                         FontSize="32"
                                         Foreground="{StaticResource PrimaryBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,15,0"/>
                                <TextBlock Text="لوحة التحكم - الوصول السريع"
                                         FontSize="28"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- Quick Actions - Compact and Beautiful -->
                        <Border Background="White"
                                CornerRadius="15"
                                Margin="0,0,0,30"
                                Padding="25"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                            </Border.Effect>

                            <StackPanel>
                                <!-- Header -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,25">
                                    <Border Background="#667eea"
                                            CornerRadius="12"
                                            Width="35" Height="35"
                                            Margin="0,0,12,0">
                                        <TextBlock Text="🚀"
                                                 FontSize="18"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Foreground="White"/>
                                    </Border>
                                    <TextBlock Text="الوصول السريع"
                                             FontSize="22"
                                             FontWeight="Bold"
                                             Foreground="#2C3E50"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Actions Grid -->
                                <ItemsControl ItemsSource="{Binding QuickActions}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="3" Rows="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White"
                                                   CornerRadius="18"
                                                   Margin="15"
                                                   Height="140"
                                                   BorderBrush="#E9ECEF"
                                                   BorderThickness="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="5" Opacity="0.25" BlurRadius="12"/>
                                                </Border.Effect>
                                                <Button Background="Transparent"
                                                      BorderThickness="0"
                                                      Cursor="Hand"
                                                      Command="{Binding DataContext.QuickActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding Action}">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                               CornerRadius="18"
                                                                               Padding="20">
                                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                            <Border.Style>
                                                                                <Style TargetType="Border">
                                                                                    <Style.Triggers>
                                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                                            <Setter Property="Background">
                                                                                                <Setter.Value>
                                                                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                                                        <GradientStop Color="#667eea" Offset="0"/>
                                                                                                        <GradientStop Color="#764ba2" Offset="1"/>
                                                                                                    </LinearGradientBrush>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                            <Setter Property="Effect">
                                                                                                <Setter.Value>
                                                                                                    <DropShadowEffect Color="#667eea" Direction="270" ShadowDepth="12" Opacity="0.4" BlurRadius="20"/>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                        </Trigger>
                                                                                    </Style.Triggers>
                                                                                </Style>
                                                                            </Border.Style>
                                                                        </Border>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <!-- Icon -->
                                                        <Border Background="#667eea"
                                                               CornerRadius="15"
                                                               Width="50" Height="50"
                                                               Margin="0,0,0,12">
                                                            <TextBlock Text="{Binding Icon}"
                                                                     FontSize="24"
                                                                     HorizontalAlignment="Center"
                                                                     VerticalAlignment="Center"
                                                                     Foreground="White"/>
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Background" Value="White"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                        </Border>
                                                        <!-- Title -->
                                                        <TextBlock Text="{Binding Title}"
                                                                 FontSize="14"
                                                                 FontWeight="Bold"
                                                                 HorizontalAlignment="Center"
                                                                 TextAlignment="Center"
                                                                 TextWrapping="Wrap"
                                                                 Foreground="#2C3E50"
                                                                 MaxWidth="120"
                                                                 Margin="0,0,0,5">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Foreground" Value="White"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                        <!-- Statistics -->
                                                        <TextBlock Text="{Binding Count}"
                                                                 FontSize="12"
                                                                 FontWeight="Bold"
                                                                 HorizontalAlignment="Center"
                                                                 Foreground="#6C757D">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Foreground" Value="#E9ECEF"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </Button>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Section -->
                        <Border Style="{StaticResource CardStyle}" Margin="0,0,0,0">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات النظام"
                                         FontSize="20"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         Margin="0,0,0,25"
                                         HorizontalAlignment="Center"/>

                                <ItemsControl ItemsSource="{Binding Statistics}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="5" Rows="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White"
                                                   CornerRadius="10"
                                                   Margin="8"
                                                   Height="100"
                                                   BorderBrush="{StaticResource PrimaryBrush}"
                                                   BorderThickness="1">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                                </Border.Effect>
                                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                                    <Border Background="{StaticResource PrimaryBrush}"
                                                           CornerRadius="15"
                                                           Width="35" Height="35"
                                                           Margin="0,0,0,8">
                                                        <TextBlock Text="{Binding Icon}"
                                                                 FontSize="16"
                                                                 HorizontalAlignment="Center"
                                                                 VerticalAlignment="Center"
                                                                 Foreground="White"/>
                                                    </Border>
                                                    <TextBlock Text="{Binding Count}"
                                                             FontSize="20"
                                                             FontWeight="Bold"
                                                             HorizontalAlignment="Center"
                                                             Foreground="{StaticResource PrimaryBrush}"/>
                                                    <TextBlock Text="{Binding Title}"
                                                             FontSize="10"
                                                             FontWeight="SemiBold"
                                                             HorizontalAlignment="Center"
                                                             Foreground="#666"
                                                             TextWrapping="Wrap"
                                                             TextAlignment="Center"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب المخططات والرسوم البيانية -->
            <TabItem Header="المخططات والتحليلات" Tag="📊">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="20">
                    <StackPanel>

                        <!-- عنوان القسم -->
                        <Grid Margin="0,0,0,30">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="📈"
                                         FontSize="32"
                                         Foreground="{StaticResource PrimaryBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,15,0"/>
                                <TextBlock Text="المخططات والتحليلات المتقدمة"
                                         FontSize="28"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- الصف الأول من المخططات -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- مخطط الزيارات الشهرية -->
                            <Border Grid.Column="0"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="0,0,15,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#2196F3"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="📅"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="الزيارات الشهرية"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- مخطط الزيارات الشهرية المبسط -->
                                    <Grid Height="300" Background="#F8F9FA">
                                        <!-- مخطط بسيط بالأعمدة -->
                                        <Grid Margin="40,20,40,60">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- أعمدة المخطط -->
                                            <Border Grid.Column="0" Background="#2196F3" CornerRadius="5,5,0,0" Height="120" VerticalAlignment="Bottom" Margin="5"/>
                                            <Border Grid.Column="1" Background="#2196F3" CornerRadius="5,5,0,0" Height="140" VerticalAlignment="Bottom" Margin="5"/>
                                            <Border Grid.Column="2" Background="#2196F3" CornerRadius="5,5,0,0" Height="90" VerticalAlignment="Bottom" Margin="5"/>
                                            <Border Grid.Column="3" Background="#2196F3" CornerRadius="5,5,0,0" Height="168" VerticalAlignment="Bottom" Margin="5"/>
                                            <Border Grid.Column="4" Background="#2196F3" CornerRadius="5,5,0,0" Height="112" VerticalAlignment="Bottom" Margin="5"/>
                                            <Border Grid.Column="5" Background="#2196F3" CornerRadius="5,5,0,0" Height="152" VerticalAlignment="Bottom" Margin="5"/>

                                            <!-- قيم الأعمدة -->
                                            <TextBlock Grid.Column="0" Text="25" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,50"/>
                                            <TextBlock Grid.Column="1" Text="35" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,70"/>
                                            <TextBlock Grid.Column="2" Text="18" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,35"/>
                                            <TextBlock Grid.Column="3" Text="42" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,84"/>
                                            <TextBlock Grid.Column="4" Text="28" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,46"/>
                                            <TextBlock Grid.Column="5" Text="38" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,76"/>
                                        </Grid>

                                        <!-- تسميات الأشهر -->
                                        <Grid VerticalAlignment="Bottom" Margin="40,0,40,20">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="يناير" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                                            <TextBlock Grid.Column="1" Text="فبراير" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                                            <TextBlock Grid.Column="2" Text="مارس" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                                            <TextBlock Grid.Column="3" Text="أبريل" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                                            <TextBlock Grid.Column="4" Text="مايو" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                                            <TextBlock Grid.Column="5" Text="يونيو" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                                        </Grid>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- مخطط حالة السائقين -->
                            <Border Grid.Column="1"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="15,0,0,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#4CAF50"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="👥"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="حالة السائقين"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- مخطط حالة السائقين المبسط -->
                                    <Grid Height="300" Background="#F8F9FA">
                                        <!-- دائرة مبسطة -->
                                        <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <Ellipse Width="180" Height="180" Fill="#E3F2FD" Stroke="#2196F3" StrokeThickness="8"/>
                                            <Ellipse Width="120" Height="120" Fill="White"/>

                                            <!-- النص المركزي -->
                                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <TextBlock Text="السائقين" FontSize="16" FontWeight="Bold" Foreground="#2C3E50" HorizontalAlignment="Center"/>
                                                <TextBlock Text="100" FontSize="24" FontWeight="Bold" Foreground="#2196F3" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Grid>

                                        <!-- المفاتيح -->
                                        <StackPanel VerticalAlignment="Bottom" HorizontalAlignment="Center" Orientation="Horizontal" Margin="0,0,0,20">
                                            <StackPanel Orientation="Horizontal" Margin="0,0,15,0">
                                                <Ellipse Width="14" Height="14" Fill="#2196F3" Margin="0,0,8,0"/>
                                                <TextBlock Text="في الميدان (45)" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,15,0">
                                                <Ellipse Width="14" Height="14" Fill="#4CAF50" Margin="0,0,8,0"/>
                                                <TextBlock Text="في المكتب (25)" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,15,0">
                                                <Ellipse Width="14" Height="14" Fill="#FF9800" Margin="0,0,8,0"/>
                                                <TextBlock Text="في إجازة (20)" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <Ellipse Width="14" Height="14" Fill="#F44336" Margin="0,0,8,0"/>
                                                <TextBlock Text="غير متاح (10)" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- الصف الثاني من المخططات -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- مخطط العقود والمشاريع -->
                            <Border Grid.Column="0"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="0,0,15,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#FF9800"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="📋"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="العقود والمشاريع"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- مخطط المشاريع المخصص -->
                                    <Grid Height="300" Background="#F8F9FA">
                                        <Canvas Name="ProjectsChart" Background="Transparent">
                                            <!-- أعمدة المشاريع -->
                                            <Rectangle Width="40" Height="120" Canvas.Left="50" Canvas.Top="150" Fill="#FF9800" RadiusX="5" RadiusY="5"/>
                                            <Rectangle Width="40" Height="180" Canvas.Left="110" Canvas.Top="90" Fill="#FF9800" RadiusX="5" RadiusY="5"/>
                                            <Rectangle Width="40" Height="150" Canvas.Left="170" Canvas.Top="120" Fill="#FF9800" RadiusX="5" RadiusY="5"/>
                                            <Rectangle Width="40" Height="220" Canvas.Left="230" Canvas.Top="50" Fill="#FF9800" RadiusX="5" RadiusY="5"/>

                                            <!-- قيم الأعمدة -->
                                            <TextBlock Text="12" Canvas.Left="62" Canvas.Top="130" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            <TextBlock Text="18" Canvas.Left="122" Canvas.Top="70" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            <TextBlock Text="15" Canvas.Left="182" Canvas.Top="100" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            <TextBlock Text="22" Canvas.Left="242" Canvas.Top="30" FontSize="12" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                        </Canvas>

                                        <!-- تسميات الفترات -->
                                        <Grid VerticalAlignment="Bottom" Margin="40,0,40,20">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="الربع الأول" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50" FontFamily="Segoe UI"/>
                                            <TextBlock Grid.Column="1" Text="الربع الثاني" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50" FontFamily="Segoe UI"/>
                                            <TextBlock Grid.Column="2" Text="الربع الثالث" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50" FontFamily="Segoe UI"/>
                                            <TextBlock Grid.Column="3" Text="الربع الرابع" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="#2C3E50" FontFamily="Segoe UI"/>
                                        </Grid>

                                        <!-- تسميات القيم -->
                                        <StackPanel VerticalAlignment="Left" HorizontalAlignment="Left" Margin="10,20,0,0">
                                            <TextBlock Text="25" FontSize="10" Foreground="#666" Margin="0,0,0,30"/>
                                            <TextBlock Text="20" FontSize="10" Foreground="#666" Margin="0,0,0,30"/>
                                            <TextBlock Text="15" FontSize="10" Foreground="#666" Margin="0,0,0,30"/>
                                            <TextBlock Text="10" FontSize="10" Foreground="#666" Margin="0,0,0,30"/>
                                            <TextBlock Text="5" FontSize="10" Foreground="#666" Margin="0,0,0,30"/>
                                            <TextBlock Text="0" FontSize="10" Foreground="#666"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- التحليل المالي -->
                            <Border Grid.Column="1"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="15,0,0,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#9C27B0"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="💰"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="التحليل المالي"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- مخطط التحليل المالي المخصص -->
                                    <Grid Height="300" Background="#F8F9FA">
                                        <Canvas Name="FinancialChart" Background="Transparent">
                                            <!-- أعمدة الميزانية المخططة -->
                                            <Rectangle Width="25" Height="150" Canvas.Left="50" Canvas.Top="120" Fill="#9C27B0" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="180" Canvas.Left="90" Canvas.Top="90" Fill="#9C27B0" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="165" Canvas.Left="130" Canvas.Top="105" Fill="#9C27B0" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="220" Canvas.Left="170" Canvas.Top="50" Fill="#9C27B0" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="195" Canvas.Left="210" Canvas.Top="75" Fill="#9C27B0" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="250" Canvas.Left="250" Canvas.Top="20" Fill="#9C27B0" RadiusX="3" RadiusY="3"/>

                                            <!-- أعمدة المصروفات الفعلية -->
                                            <Rectangle Width="25" Height="120" Canvas.Left="80" Canvas.Top="150" Fill="#E91E63" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="145" Canvas.Left="120" Canvas.Top="125" Fill="#E91E63" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="135" Canvas.Left="160" Canvas.Top="135" Fill="#E91E63" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="185" Canvas.Left="200" Canvas.Top="85" Fill="#E91E63" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="170" Canvas.Left="240" Canvas.Top="100" Fill="#E91E63" RadiusX="3" RadiusY="3"/>
                                            <Rectangle Width="25" Height="210" Canvas.Left="280" Canvas.Top="60" Fill="#E91E63" RadiusX="3" RadiusY="3"/>
                                        </Canvas>

                                        <!-- تسميات الأشهر -->
                                        <Grid VerticalAlignment="Bottom" Margin="40,0,40,20">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="يناير" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                            <TextBlock Grid.Column="1" Text="فبراير" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                            <TextBlock Grid.Column="2" Text="مارس" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                            <TextBlock Grid.Column="3" Text="أبريل" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                            <TextBlock Grid.Column="4" Text="مايو" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                            <TextBlock Grid.Column="5" Text="يونيو" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        </Grid>

                                        <!-- المفاتيح -->
                                        <StackPanel VerticalAlignment="Top" HorizontalAlignment="Right" Orientation="Horizontal" Margin="0,20,20,0">
                                            <StackPanel Orientation="Horizontal" Margin="0,0,20,0">
                                                <Rectangle Width="14" Height="14" Fill="#9C27B0" Margin="0,0,8,0" RadiusX="2" RadiusY="2"/>
                                                <TextBlock Text="الميزانية المخططة" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <Rectangle Width="14" Height="14" Fill="#E91E63" Margin="0,0,8,0" RadiusX="2" RadiusY="2"/>
                                                <TextBlock Text="المصروفات الفعلية" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- مخطط شامل للأداء -->
                        <Border Background="White"
                                CornerRadius="15"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1"
                                Margin="0,0,0,30"
                                Padding="20">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                            </Border.Effect>
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20" HorizontalAlignment="Center">
                                    <Border Background="#F44336"
                                            CornerRadius="8"
                                            Width="35" Height="35"
                                            Margin="0,0,15,0">
                                        <TextBlock Text="📊"
                                                 FontSize="16"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Foreground="White"/>
                                    </Border>
                                    <TextBlock Text="تحليل الأداء الشامل - مقارنة شهرية"
                                             FontSize="20"
                                             FontWeight="Bold"
                                             Foreground="#2C3E50"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- مخطط الأداء الشامل المخصص -->
                                <Grid Height="400" Background="#F8F9FA">
                                    <Canvas Name="PerformanceChart" Background="Transparent">
                                        <!-- خط الزيارات -->
                                        <Polyline Points="50,350 100,320 150,280 200,250 250,220 300,180 350,150 400,120"
                                                  Stroke="#2196F3" StrokeThickness="3" Fill="Transparent"/>

                                        <!-- خط العقود -->
                                        <Polyline Points="50,330 100,300 150,270 200,240 250,200 300,170 350,140 400,110"
                                                  Stroke="#4CAF50" StrokeThickness="3" Fill="Transparent"/>

                                        <!-- خط الميزانية -->
                                        <Polyline Points="50,340 100,310 150,290 200,260 250,230 300,190 350,160 400,130"
                                                  Stroke="#FF9800" StrokeThickness="3" Fill="Transparent"/>

                                        <!-- نقاط البيانات للزيارات -->
                                        <Ellipse Width="8" Height="8" Canvas.Left="46" Canvas.Top="346" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="96" Canvas.Top="316" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="146" Canvas.Top="276" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="196" Canvas.Top="246" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="246" Canvas.Top="216" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="296" Canvas.Top="176" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="346" Canvas.Top="146" Fill="#2196F3"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="396" Canvas.Top="116" Fill="#2196F3"/>

                                        <!-- نقاط البيانات للعقود -->
                                        <Ellipse Width="8" Height="8" Canvas.Left="46" Canvas.Top="326" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="96" Canvas.Top="296" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="146" Canvas.Top="266" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="196" Canvas.Top="236" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="246" Canvas.Top="196" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="296" Canvas.Top="166" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="346" Canvas.Top="136" Fill="#4CAF50"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="396" Canvas.Top="106" Fill="#4CAF50"/>

                                        <!-- نقاط البيانات للميزانية -->
                                        <Ellipse Width="8" Height="8" Canvas.Left="46" Canvas.Top="336" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="96" Canvas.Top="306" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="146" Canvas.Top="286" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="196" Canvas.Top="256" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="246" Canvas.Top="226" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="296" Canvas.Top="186" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="346" Canvas.Top="156" Fill="#FF9800"/>
                                        <Ellipse Width="8" Height="8" Canvas.Left="396" Canvas.Top="126" Fill="#FF9800"/>
                                    </Canvas>

                                    <!-- تسميات الأشهر -->
                                    <Grid VerticalAlignment="Bottom" Margin="40,0,40,20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="يناير" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="1" Text="فبراير" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="2" Text="مارس" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="3" Text="أبريل" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="4" Text="مايو" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="5" Text="يونيو" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="6" Text="يوليو" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                        <TextBlock Grid.Column="7" Text="أغسطس" FontSize="10" HorizontalAlignment="Center" Foreground="#666"/>
                                    </Grid>

                                    <!-- المفاتيح -->
                                    <StackPanel VerticalAlignment="Top" HorizontalAlignment="Right" Orientation="Horizontal" Margin="0,20,20,0">
                                        <StackPanel Orientation="Horizontal" Margin="0,0,20,0">
                                            <Ellipse Width="14" Height="14" Fill="#2196F3" Margin="0,0,8,0"/>
                                            <TextBlock Text="الزيارات الميدانية" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,20,0">
                                            <Ellipse Width="14" Height="14" Fill="#4CAF50" Margin="0,0,8,0"/>
                                            <TextBlock Text="العقود المنجزة" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal">
                                            <Ellipse Width="14" Height="14" Fill="#FF9800" Margin="0,0,8,0"/>
                                            <TextBlock Text="الميزانية المستخدمة" FontSize="11" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
