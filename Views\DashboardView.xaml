<UserControl x:Class="DriverManagementSystem.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- تعريف الألوان للمخططات -->
            <SolidColorBrush x:Key="ChartPrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="ChartSecondaryBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="ChartAccentBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ChartDangerBrush" Color="#F44336"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- التبويبات الرئيسية -->
        <TabControl Background="Transparent"
                    BorderThickness="0"
                    Margin="20">
            <TabControl.Resources>
                <!-- تصميم التبويبات -->
                <Style TargetType="TabItem">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border Name="Border"
                                        Background="White"
                                        BorderBrush="#E0E0E0"
                                        BorderThickness="1,1,1,0"
                                        CornerRadius="10,10,0,0"
                                        Margin="2,0"
                                        Padding="20,12">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                                    </Border.Effect>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Tag}"
                                                   FontSize="18"
                                                   Margin="0,0,8,0"
                                                   VerticalAlignment="Center"/>
                                        <ContentPresenter ContentSource="Header"
                                                          VerticalAlignment="Center"
                                                          HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#2196F3"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Setter Property="FontSize" Value="16"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                    <Setter Property="Foreground" Value="#2C3E50"/>
                </Style>
            </TabControl.Resources>

            <!-- تبويب الوصول السريع -->
            <TabItem Header="الوصول السريع" Tag="🚀">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="20">
                    <StackPanel>

                        <!-- Page Header -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Orientation="Horizontal" Grid.Column="0">
                                <TextBlock Text="🏠"
                                         FontSize="32"
                                         Foreground="{StaticResource PrimaryBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,15,0"/>
                                <TextBlock Text="لوحة التحكم - الوصول السريع"
                                         FontSize="28"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- Quick Actions - Compact and Beautiful -->
                        <Border Background="White"
                                CornerRadius="15"
                                Margin="0,0,0,30"
                                Padding="25"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                            </Border.Effect>

                            <StackPanel>
                                <!-- Header -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,25">
                                    <Border Background="#667eea"
                                            CornerRadius="12"
                                            Width="35" Height="35"
                                            Margin="0,0,12,0">
                                        <TextBlock Text="🚀"
                                                 FontSize="18"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Foreground="White"/>
                                    </Border>
                                    <TextBlock Text="الوصول السريع"
                                             FontSize="22"
                                             FontWeight="Bold"
                                             Foreground="#2C3E50"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Actions Grid -->
                                <ItemsControl ItemsSource="{Binding QuickActions}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="3" Rows="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White"
                                                   CornerRadius="18"
                                                   Margin="15"
                                                   Height="140"
                                                   BorderBrush="#E9ECEF"
                                                   BorderThickness="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="5" Opacity="0.25" BlurRadius="12"/>
                                                </Border.Effect>
                                                <Button Background="Transparent"
                                                      BorderThickness="0"
                                                      Cursor="Hand"
                                                      Command="{Binding DataContext.QuickActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding Action}">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                               CornerRadius="18"
                                                                               Padding="20">
                                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                            <Border.Style>
                                                                                <Style TargetType="Border">
                                                                                    <Style.Triggers>
                                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                                            <Setter Property="Background">
                                                                                                <Setter.Value>
                                                                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                                                        <GradientStop Color="#667eea" Offset="0"/>
                                                                                                        <GradientStop Color="#764ba2" Offset="1"/>
                                                                                                    </LinearGradientBrush>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                            <Setter Property="Effect">
                                                                                                <Setter.Value>
                                                                                                    <DropShadowEffect Color="#667eea" Direction="270" ShadowDepth="12" Opacity="0.4" BlurRadius="20"/>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                        </Trigger>
                                                                                    </Style.Triggers>
                                                                                </Style>
                                                                            </Border.Style>
                                                                        </Border>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <!-- Icon -->
                                                        <Border Background="#667eea"
                                                               CornerRadius="15"
                                                               Width="50" Height="50"
                                                               Margin="0,0,0,12">
                                                            <TextBlock Text="{Binding Icon}"
                                                                     FontSize="24"
                                                                     HorizontalAlignment="Center"
                                                                     VerticalAlignment="Center"
                                                                     Foreground="White"/>
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Background" Value="White"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                        </Border>
                                                        <!-- Title -->
                                                        <TextBlock Text="{Binding Title}"
                                                                 FontSize="14"
                                                                 FontWeight="Bold"
                                                                 HorizontalAlignment="Center"
                                                                 TextAlignment="Center"
                                                                 TextWrapping="Wrap"
                                                                 Foreground="#2C3E50"
                                                                 MaxWidth="120"
                                                                 Margin="0,0,0,5">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Foreground" Value="White"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                        <!-- Statistics -->
                                                        <TextBlock Text="{Binding Count}"
                                                                 FontSize="12"
                                                                 FontWeight="Bold"
                                                                 HorizontalAlignment="Center"
                                                                 Foreground="#6C757D">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                                                                            <Setter Property="Foreground" Value="#E9ECEF"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </Button>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Section -->
                        <Border Style="{StaticResource CardStyle}" Margin="0,0,0,0">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات النظام"
                                         FontSize="20"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         Margin="0,0,0,25"
                                         HorizontalAlignment="Center"/>

                                <ItemsControl ItemsSource="{Binding Statistics}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="5" Rows="1"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="White"
                                                   CornerRadius="10"
                                                   Margin="8"
                                                   Height="100"
                                                   BorderBrush="{StaticResource PrimaryBrush}"
                                                   BorderThickness="1">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                                                </Border.Effect>
                                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                                    <Border Background="{StaticResource PrimaryBrush}"
                                                           CornerRadius="15"
                                                           Width="35" Height="35"
                                                           Margin="0,0,0,8">
                                                        <TextBlock Text="{Binding Icon}"
                                                                 FontSize="16"
                                                                 HorizontalAlignment="Center"
                                                                 VerticalAlignment="Center"
                                                                 Foreground="White"/>
                                                    </Border>
                                                    <TextBlock Text="{Binding Count}"
                                                             FontSize="20"
                                                             FontWeight="Bold"
                                                             HorizontalAlignment="Center"
                                                             Foreground="{StaticResource PrimaryBrush}"/>
                                                    <TextBlock Text="{Binding Title}"
                                                             FontSize="10"
                                                             FontWeight="SemiBold"
                                                             HorizontalAlignment="Center"
                                                             Foreground="#666"
                                                             TextWrapping="Wrap"
                                                             TextAlignment="Center"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب المخططات والرسوم البيانية -->
            <TabItem Header="المخططات والتحليلات" Tag="📊">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="20">
                    <StackPanel>

                        <!-- عنوان القسم -->
                        <Grid Margin="0,0,0,30">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="📈"
                                         FontSize="32"
                                         Foreground="{StaticResource PrimaryBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,15,0"/>
                                <TextBlock Text="المخططات والتحليلات المتقدمة"
                                         FontSize="28"
                                         FontWeight="Bold"
                                         Foreground="{StaticResource TextPrimaryBrush}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- الصف الأول من المخططات -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- مخطط الزيارات الشهرية -->
                            <Border Grid.Column="0"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="0,0,15,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#2196F3"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="📅"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="الزيارات الشهرية"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <lvc:CartesianChart Series="{Binding VisitsChartSeries}"
                                                        XAxes="{Binding VisitsChartXAxes}"
                                                        YAxes="{Binding VisitsChartYAxes}"
                                                        Height="300"
                                                        Background="Transparent"
                                                        FlowDirection="LeftToRight"/>
                                </StackPanel>
                            </Border>

                            <!-- مخطط حالة السائقين -->
                            <Border Grid.Column="1"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="15,0,0,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#4CAF50"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="👥"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="حالة السائقين"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <lvc:PieChart Series="{Binding DriversStatusChartSeries}"
                                                  Height="300"
                                                  Background="Transparent"
                                                  FlowDirection="LeftToRight"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- الصف الثاني من المخططات -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- مخطط العقود والمشاريع -->
                            <Border Grid.Column="0"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="0,0,15,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#FF9800"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="📋"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="العقود والمشاريع"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <lvc:CartesianChart Series="{Binding ProjectsChartSeries}"
                                                        XAxes="{Binding ProjectsChartXAxes}"
                                                        YAxes="{Binding ProjectsChartYAxes}"
                                                        Height="300"
                                                        Background="Transparent"
                                                        FlowDirection="LeftToRight"/>
                                </StackPanel>
                            </Border>

                            <!-- التحليل المالي -->
                            <Border Grid.Column="1"
                                    Background="White"
                                    CornerRadius="15"
                                    BorderBrush="#E0E0E0"
                                    BorderThickness="1"
                                    Margin="15,0,0,0"
                                    Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Border Background="#9C27B0"
                                                CornerRadius="8"
                                                Width="30" Height="30"
                                                Margin="0,0,10,0">
                                            <TextBlock Text="💰"
                                                     FontSize="14"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="التحليل المالي"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 Foreground="#2C3E50"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <lvc:CartesianChart Series="{Binding FinancialChartSeries}"
                                                        XAxes="{Binding FinancialChartXAxes}"
                                                        YAxes="{Binding FinancialChartYAxes}"
                                                        Height="300"
                                                        Background="Transparent"
                                                        FlowDirection="LeftToRight"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- مخطط شامل للأداء -->
                        <Border Background="White"
                                CornerRadius="15"
                                BorderBrush="#E0E0E0"
                                BorderThickness="1"
                                Margin="0,0,0,30"
                                Padding="20">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                            </Border.Effect>
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20" HorizontalAlignment="Center">
                                    <Border Background="#F44336"
                                            CornerRadius="8"
                                            Width="35" Height="35"
                                            Margin="0,0,15,0">
                                        <TextBlock Text="📊"
                                                 FontSize="16"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Foreground="White"/>
                                    </Border>
                                    <TextBlock Text="تحليل الأداء الشامل - مقارنة شهرية"
                                             FontSize="20"
                                             FontWeight="Bold"
                                             Foreground="#2C3E50"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <lvc:CartesianChart Series="{Binding PerformanceChartSeries}"
                                                    XAxes="{Binding PerformanceChartXAxes}"
                                                    YAxes="{Binding PerformanceChartYAxes}"
                                                    Height="400"
                                                    Background="Transparent"
                                                    FlowDirection="LeftToRight"/>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
